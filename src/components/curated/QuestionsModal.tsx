import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import * as Dialog from '@radix-ui/react-dialog'
import {
  X,
  Play,
  BookOpen,
  Clock,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { ContentSet, Question, CuratedService } from '../../services/curatedService'
import { cn } from '../../utils/cn'

interface QuestionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  contentSet: ContentSet | null
  onTryThis: (taskSetId: string) => void
}

/**
 * Modal for displaying questions from a curated content set
 * with a "Try This" button to convert to task set
 */
const QuestionsModal: React.FC<QuestionsModalProps> = ({
  open,
  onOpenChange,
  contentSet,
  onTryThis
}) => {
  const [questions, setQuestions] = useState<Question[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [converting, setConverting] = useState(false)

  // Fetch questions when modal opens and content set is available
  useEffect(() => {
    if (open && contentSet) {
      fetchQuestions()
    }
  }, [open, contentSet])

  const fetchQuestions = async () => {
    if (!contentSet) return

    setLoading(true)
    setError(null)
    try {
      const questionsData = await CuratedService.getQuestions(contentSet.id || contentSet._id)
      setQuestions(questionsData)
    } catch (err) {
      console.error('Error fetching questions:', err)
      setError('Failed to load questions. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleTryThis = async () => {
    if (!contentSet) return

    setConverting(true)
    try {
      const response = await CuratedService.convertToTaskSet(contentSet.id || contentSet._id)
      // Handle the new API response format
      const taskSetId = response.data.task_set_id
      onTryThis(taskSetId)
      onOpenChange(false)
    } catch (err) {
      console.error('Error converting to task set:', err)
      setError('Failed to create task set. Please try again.')
    } finally {
      setConverting(false)
    }
  }

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'text-green-600 bg-green-50 border-green-200'
      case 2: return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 3: return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getDifficultyLabel = (level: number) => {
    switch (level) {
      case 1: return 'Easy'
      case 2: return 'Medium'
      case 3: return 'Hard'
      default: return 'Unknown'
    }
  }

  if (!contentSet) return null

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-4xl max-h-[90vh] overflow-hidden">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-4">
                {contentSet.theme && (
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{contentSet.theme.icon}</span>
                    <span className="text-sm text-muted-foreground font-medium">
                      {contentSet.theme.name_en}
                    </span>
                  </div>
                )}
                <div>
                  <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                    {contentSet.title_en || contentSet.title}
                  </h2>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    {contentSet.description_en || contentSet.description}
                  </p>
                </div>
              </div>
              <Dialog.Close className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                <X className="w-5 h-5" />
              </Dialog.Close>
            </div>

            {/* Content Info */}
            <div className="px-6 py-4 bg-slate-50 dark:bg-slate-800/50 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <BookOpen className="w-4 h-4 text-blue-500" />
                  <span className="text-slate-600 dark:text-slate-400">
                    {contentSet.total_items} Questions
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-green-500" />
                  <span className="text-slate-600 dark:text-slate-400">
                    ~{Math.ceil(contentSet.total_items * 1.5)} min
                  </span>
                </div>
                <div className={cn(
                  'px-2 py-1 rounded-full text-xs font-medium border',
                  getDifficultyColor(contentSet.difficulty_level)
                )}>
                  {getDifficultyLabel(contentSet.difficulty_level)}
                </div>
              </div>
            </div>

            {/* Questions Content */}
            <div className="p-6 max-h-96 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                  <span className="ml-3 text-slate-600 dark:text-slate-400">
                    Loading questions...
                  </span>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-12 text-red-500">
                  <AlertCircle className="w-8 h-8" />
                  <span className="ml-3">{error}</span>
                </div>
              ) : questions.length === 0 ? (
                <div className="text-center py-12 text-slate-500">
                  No questions available for this content set.
                </div>
              ) : (
                <div className="space-y-4">
                  {questions.map((question, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 flex items-center justify-center text-sm font-medium flex-shrink-0 mt-1">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <p className="text-slate-900 dark:text-white font-medium mb-2">
                            {question.question.text}
                          </p>
                          {question.question.translated_text && (
                            <p className="text-slate-600 dark:text-slate-400 text-sm mb-3">
                              {question.question.translated_text}
                            </p>
                          )}
                          {question.question.options && (
                            <div className="space-y-1">
                              {Object.entries(question.question.options).map(([key, value]) => (
                                <div key={key} className="flex items-center gap-2 text-sm">
                                  <span className="w-6 h-6 rounded border border-slate-300 dark:border-slate-600 flex items-center justify-center text-xs font-medium">
                                    {key.toUpperCase()}
                                  </span>
                                  <span className="text-slate-700 dark:text-slate-300">{value}</span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer with Try This button */}
            <div className="px-6 py-4 bg-slate-50 dark:bg-slate-800/50 border-t border-slate-200 dark:border-slate-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  Ready to start this quiz?
                </div>
                <button
                  onClick={handleTryThis}
                  disabled={converting || loading}
                  className={cn(
                    'px-6 py-2 rounded-lg font-medium transition-all duration-200',
                    'bg-gradient-to-r from-blue-500 to-indigo-600 text-white',
                    'hover:from-blue-600 hover:to-indigo-700 hover:shadow-lg',
                    'disabled:opacity-50 disabled:cursor-not-allowed',
                    'flex items-center gap-2'
                  )}
                >
                  {converting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      Try This
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

export default QuestionsModal
