/**
 * DateTime Helper Utility
 * Handles conversion from UTC to local time and provides consistent date formatting
 */

/**
 * Converts UTC date string to local Date object
 * @param utcDateString - UTC date string from API
 * @returns Local Date object
 */
export const utcToLocal = (utcDateString: string): Date => {
  return new Date(utcDateString)
}

/**
 * Formats a UTC date string to local date string
 * @param utcDateString - UTC date string from API
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted local date string
 */
export const formatLocalDate = (
  utcDateString: string, 
  options?: Intl.DateTimeFormatOptions
): string => {
  const localDate = utcToLocal(utcDateString)
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }
  return localDate.toLocaleDateString(undefined, options || defaultOptions)
}

/**
 * Formats a UTC date string to local time string
 * @param utcDateString - UTC date string from API
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted local time string
 */
export const formatLocalTime = (
  utcDateString: string,
  options?: Intl.DateTimeFormatOptions
): string => {
  const localDate = utcToLocal(utcDateString)
  const defaultOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  }
  return localDate.toLocaleTimeString(undefined, options || defaultOptions)
}

/**
 * Formats a UTC date string to local date and time string
 * @param utcDateString - UTC date string from API
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted local date and time string
 */
export const formatLocalDateTime = (
  utcDateString: string,
  options?: Intl.DateTimeFormatOptions
): string => {
  const localDate = utcToLocal(utcDateString)
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  }
  return localDate.toLocaleString(undefined, options || defaultOptions)
}

/**
 * Formats a UTC date string as "time ago" (e.g., "2h ago", "3d ago")
 * @param utcDateString - UTC date string from API
 * @returns Formatted time ago string
 */
export const formatTimeAgo = (utcDateString: string): string => {
  const localDate = utcToLocal(utcDateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - localDate.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return `${Math.floor(diffInSeconds / 2592000)}mo ago`
}

/**
 * Formats a UTC date string for display with weekday
 * @param utcDateString - UTC date string from API
 * @returns Formatted date with weekday (e.g., "Monday, Dec 25, 2023")
 */
export const formatLocalDateWithWeekday = (utcDateString: string): string => {
  const localDate = utcToLocal(utcDateString)
  return localDate.toLocaleDateString(undefined, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

/**
 * Gets current date in a readable format
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted current date string
 */
export const getCurrentDate = (options?: Intl.DateTimeFormatOptions): string => {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  return new Date().toLocaleDateString(undefined, options || defaultOptions)
}

/**
 * Checks if a UTC date string is today
 * @param utcDateString - UTC date string from API
 * @returns True if the date is today in local time
 */
export const isToday = (utcDateString: string): boolean => {
  const localDate = utcToLocal(utcDateString)
  const today = new Date()
  return localDate.toDateString() === today.toDateString()
}

/**
 * Checks if a UTC date string is yesterday
 * @param utcDateString - UTC date string from API
 * @returns True if the date is yesterday in local time
 */
export const isYesterday = (utcDateString: string): boolean => {
  const localDate = utcToLocal(utcDateString)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return localDate.toDateString() === yesterday.toDateString()
}

/**
 * Formats a UTC date string with relative context (Today, Yesterday, or date)
 * @param utcDateString - UTC date string from API
 * @returns Formatted date with context
 */
export const formatRelativeDate = (utcDateString: string): string => {
  if (isToday(utcDateString)) {
    return `Today, ${formatLocalTime(utcDateString)}`
  }
  if (isYesterday(utcDateString)) {
    return `Yesterday, ${formatLocalTime(utcDateString)}`
  }
  return formatLocalDate(utcDateString)
}
