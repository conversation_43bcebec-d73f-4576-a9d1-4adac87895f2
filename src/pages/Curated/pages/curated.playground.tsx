import React, { useState, useCallback } from 'react'
import {
  <PERSON>,
  AlertTriangle,
  Wand2
} from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import { PromptEditor, PromptHistory } from '../components'
import { useAppSelector } from '../../../store/hooks'

/**
 * Playground Page
 * Admin-only content generation interface
 * Provides prompt editor and generation history
 */
const Playground: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const user = useAppSelector(state => state.auth.user)

  // Check if user has admin privileges
  // Note: This should be replaced with proper role checking from your auth system
  const isAdmin = user?.role === 'admin' || user?.role === 'agent'

  const handleGenerate = useCallback((_prompt: string) => {
    // Trigger refresh of prompt history
    setRefreshTrigger(prev => prev + 1)
  }, [])

  // Admin access check
  if (!isAdmin) {
    return (
      <MainLayout
        title="⚡ Playground"
        description="Content generation workspace"
        className="h-screen flex items-center justify-center"
      >
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mb-4 mx-auto">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-bold mb-2">Access Restricted</h2>
          <p className="text-gray-600 mb-4">Admin privileges required</p>
          <div className="flex items-center gap-2 px-3 py-2 bg-yellow-100 rounded-lg">
            <AlertTriangle className="w-4 h-4 text-yellow-600" />
            <span className="text-sm text-yellow-800">Contact administrator</span>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout
      title="⚡ Playground"
      description="AI Content Generation"
      className="h-screen overflow-hidden"
    >
      {/* Simple Header */}
      <div className="flex items-center justify-between mb-6 p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white">
        <div className="flex items-center gap-3">
          <Wand2 className="w-6 h-6" />
          <div>
            <h1 className="text-xl font-bold">AI Content Playground</h1>
            <p className="text-sm text-blue-100">Generate educational content</p>
          </div>
        </div>
        <div className="flex items-center gap-2 px-3 py-1 bg-white/20 rounded-lg">
          <Shield className="w-4 h-4" />
          <span className="text-sm font-medium">Admin</span>
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 h-[calc(100vh-200px)]">
        {/* Prompt Editor - 60% width */}
        <div className="lg:col-span-3">
          <PromptEditor
            onGenerate={handleGenerate}
            className="h-full"
          />
        </div>

        {/* Prompt History - 40% width */}
        <div className="lg:col-span-2">
          <PromptHistory
            refreshTrigger={refreshTrigger}
            className="h-full"
          />
        </div>
      </div>
    </MainLayout>
  )
}

export default Playground
