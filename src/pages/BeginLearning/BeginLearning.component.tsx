import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>if<PERSON>,
  Wif<PERSON>Off,
  <PERSON><PERSON>s,
  Play,
  Square,
  Loader2,
  CheckCircle,
  AlertCircle,
  Volume2,
  Headphones,
  Zap,
  Target,
  BookOpen,
  Sparkles,
  FileAudio
} from 'lucide-react'
import MainLayout from '../../components/layout/MainLayout'
import { cn } from '../../utils/cn'
import { SocketSession, SocketConnectionState } from '../../services/socket/socketService'
import PersistentStoryToggle from '../../components/common/PersistentStoryToggle'

interface BeginLearningComponentProps {
  connectionState: SocketConnectionState['status']
  session: SocketSession | null
  isRecording: boolean
  error: string | null
  isLoading: boolean
  difficulty: 'easy' | 'medium' | 'hard'
  numTasks: number
  taskGenerationProgress: {
    processing: boolean
    completed: boolean
    taskSetId?: string
  }
  onStartRecording: () => void
  onStopRecording: () => void
  onDisconnect: () => void
  onSettingsChange: (settings: { difficulty: 'easy' | 'medium' | 'hard', numTasks: number }) => void
  onClearError: () => void
  // Story mode props
  isStoryModeEnabled: boolean
  recordedAudioBlob: Blob | null
  isGeneratingStory: boolean
  storyGenerationError: string | null
  onToggleStoryMode: () => void
  onGenerateStory: () => void
  onClearStoryError: () => void
}

/**
 * Begin Learning Component - Modern UI for audio learning sessions
 */
const BeginLearningComponent: React.FC<BeginLearningComponentProps> = ({
  connectionState,
  session,
  isRecording,
  error,
  isLoading,
  difficulty,
  numTasks,
  taskGenerationProgress,
  onStartRecording,
  onStopRecording,
  onDisconnect,
  onSettingsChange,
  onClearError,
  // Story mode props
  isStoryModeEnabled,
  recordedAudioBlob,
  isGeneratingStory,
  storyGenerationError,
  onToggleStoryMode,
  onGenerateStory,
  onClearStoryError
}) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  }

  const pulseVariants = {
    pulse: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  // Status indicators
  const getStatusColor = () => {
    switch (connectionState) {
      case 'CONNECTED': return 'text-green-500'
      case 'ACTIVE': return 'text-blue-500'
      case 'COMPLETED': return 'text-purple-500'
      case 'ERROR': return 'text-red-500'
      case 'CANCELLED': return 'text-orange-500'
      default: return 'text-gray-500'
    }
  }

  const getStatusIcon = () => {
    switch (connectionState) {
      case 'CONNECTED':
      case 'ACTIVE':
        return <Wifi className="h-5 w-5" />
      case 'COMPLETED':
        return <CheckCircle className="h-5 w-5" />
      case 'ERROR':
        return <AlertCircle className="h-5 w-5" />
      default:
        return <WifiOff className="h-5 w-5" />
    }
  }

  const isConnected = connectionState === 'CONNECTED' || connectionState === 'ACTIVE'
  const canStartRecording = !isRecording && !taskGenerationProgress.processing && !isLoading

  return (
    <MainLayout
      title="Begin Learning"
      description="Start your interactive Nepali learning session"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-4xl mx-auto space-y-4 sm:space-y-6 lg:space-y-8"
      >
        {/* Status Header */}
        <motion.div
          variants={cardVariants}
          className="bg-card border border-border rounded-xl p-4 sm:p-6"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
            <div className="flex items-center gap-3 sm:gap-4 min-w-0">
              <div className={cn("flex items-center gap-2", getStatusColor())}>
                {getStatusIcon()}
                <span className="font-medium capitalize text-sm sm:text-base">
                  {connectionState.toLowerCase().replace('_', ' ')}
                </span>
              </div>
              {session && (
                <div className="text-xs sm:text-sm text-muted-foreground truncate">
                  Session: {session.session_id.slice(0, 8)}...
                </div>
              )}
            </div>

            <div className="flex items-center gap-2 sm:gap-3">
              <div className="text-xs sm:text-sm text-muted-foreground">
                {difficulty} • {numTasks} tasks
              </div>
              <button
                onClick={() => {/* Settings modal */}}
                className="p-1.5 sm:p-2 hover:bg-accent rounded-lg transition-colors"
              >
                <Settings className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </button>
            </div>
          </div>
        </motion.div>

        {/* Story Mode Toggle */}
        <motion.div
          variants={cardVariants}
          className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border border-border rounded-xl p-4 sm:p-6"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <BookOpen className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h3 className="font-medium text-foreground">Story Mode</h3>
                <p className="text-sm text-muted-foreground">
                  Generate interactive stories from your recordings
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors bg-purple-600"
              )}>
                <span className="inline-block h-4 w-4 transform rounded-full bg-white shadow-lg ml-5" />
              </div>

              <span className="text-sm font-medium text-foreground">
                Enabled
              </span>
            </div>
          </div>

          {/* Story Mode Status */}
          <div className="mt-4 pt-4 border-t border-border">
            <div className="flex flex-col gap-3">
              <div className="flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400">
                <Sparkles className="h-4 w-4" />
                <span>Stories will be generated automatically after recording</span>
              </div>

                  {isGeneratingStory && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3"
                    >
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Generating story in parallel...</span>
                    </motion.div>
                  )}

                  {recordedAudioBlob && !isGeneratingStory && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="flex items-center justify-between gap-3 text-sm text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 rounded-lg p-3"
                    >
                      <div className="flex items-center gap-2">
                        <FileAudio className="h-4 w-4" />
                        <span>Audio ready for story generation</span>
                      </div>

                      <motion.button
                        onClick={onGenerateStory}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="flex items-center gap-1 px-3 py-1 rounded-md bg-green-600 text-white text-xs font-medium hover:bg-green-700 transition-colors"
                      >
                        <Sparkles className="h-3 w-3" />
                        Retry
                      </motion.button>
                    </motion.div>
                  )}
            </div>
          </div>
        </motion.div>

        {/* Persistent Story Toggle */}
        <PersistentStoryToggle variant="default" />

        {/* Story Generation Error */}
        <AnimatePresence>
          {storyGenerationError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4"
            >
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
                <div className="flex-1">
                  <h3 className="font-medium text-red-800 dark:text-red-200">Story Generation Error</h3>
                  <p className="text-sm text-red-600 dark:text-red-300 mt-1">{storyGenerationError}</p>
                </div>
                <button
                  onClick={onClearStoryError}
                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4"
            >
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
                <div className="flex-1">
                  <h3 className="font-medium text-red-800 dark:text-red-200">Error</h3>
                  <p className="text-sm text-red-600 dark:text-red-300 mt-1">{error}</p>
                </div>
                <button
                  onClick={onClearError}
                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Control Panel */}
        <motion.div
          variants={cardVariants}
          className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border border-border rounded-xl p-4 sm:p-6 lg:p-8"
        >
          <div className="text-center space-y-4 sm:space-y-6">
            <div className="space-y-2">
              <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground">
                Interactive Learning Session
              </h2>
              <p className="text-sm sm:text-base text-muted-foreground px-2">
                Connect and start speaking to generate personalized learning tasks
              </p>
            </div>

            {/* Recording Controls */}
            <div className="flex flex-col items-center gap-4 sm:gap-6">
              <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4">
                {!isRecording ? (
                  <motion.button
                    variants={pulseVariants}
                    animate={canStartRecording ? "pulse" : undefined}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={onStartRecording}
                    disabled={!canStartRecording}
                    className={cn(
                      "flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-medium transition-all",
                      "bg-green-600 hover:bg-green-700 text-white text-sm sm:text-base",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      "shadow-lg hover:shadow-xl w-full sm:w-auto"
                    )}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 sm:h-5 sm:w-5 animate-spin" />
                    ) : (
                      <Mic className="h-4 w-4 sm:h-5 sm:w-5" />
                    )}
                    {isLoading ? 'Starting...' : 'Start Recording'}
                  </motion.button>
                ) : (
                  <motion.button
                    variants={pulseVariants}
                    animate="pulse"
                    onClick={onStopRecording}
                    className={cn(
                      "flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-medium transition-all",
                      "bg-red-600 hover:bg-red-700 text-white text-sm sm:text-base",
                      "shadow-lg hover:shadow-xl w-full sm:w-auto"
                    )}
                  >
                    <Square className="h-4 w-4 sm:h-5 sm:w-5" />
                    Stop Recording
                  </motion.button>
                )}

                {(isConnected || isRecording) && (
                  <button
                    onClick={onDisconnect}
                    className="px-3 sm:px-4 py-2 text-xs sm:text-sm border border-border rounded-lg hover:bg-accent transition-colors w-full sm:w-auto"
                  >
                    Disconnect
                  </button>
                )}
              </div>
            </div>

            {/* Recording Indicator */}
            <AnimatePresence>
              {isRecording && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center gap-3 text-red-600"
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="w-3 h-3 bg-red-600 rounded-full"
                  />
                  <span className="font-medium">Recording in progress...</span>
                  <Volume2 className="h-4 w-4" />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Parallel Processing Progress */}
        <AnimatePresence>
          {(taskGenerationProgress.processing || isGeneratingStory) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              variants={cardVariants}
              className="bg-card border border-border rounded-xl p-6 space-y-4"
            >
              {/* Task Generation */}
              {taskGenerationProgress.processing && (
                <div className="flex items-center gap-4">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                  <div>
                    <h3 className="font-medium text-foreground">Generating Learning Tasks</h3>
                    <p className="text-sm text-muted-foreground">
                      Processing your audio to create personalized tasks...
                    </p>
                  </div>
                </div>
              )}

              {/* Story Generation */}
              {isGeneratingStory && (
                <div className="flex items-center gap-4">
                  <Loader2 className="h-6 w-6 animate-spin text-purple-600" />
                  <div>
                    <h3 className="font-medium text-foreground">Generating Interactive Story</h3>
                    <p className="text-sm text-muted-foreground">
                      Creating your personalized story experience...
                    </p>
                  </div>
                </div>
              )}

              {/* Combined message */}
              {taskGenerationProgress.processing && isGeneratingStory && (
                <div className="pt-2 border-t border-border">
                  <p className="text-xs text-muted-foreground">
                    Both tasks and story are being generated in parallel. Please keep this page open.
                  </p>
                </div>
              )}

              {/* Single process message */}
              {(taskGenerationProgress.processing || isGeneratingStory) && !(taskGenerationProgress.processing && isGeneratingStory) && (
                <div className="pt-2">
                  <p className="text-xs text-muted-foreground">
                    Please keep this page open while we process your audio.
                  </p>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {taskGenerationProgress.completed && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              variants={cardVariants}
              className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6"
            >
              <div className="flex items-center gap-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-medium text-green-800 dark:text-green-200">
                    Tasks Generated Successfully!
                  </h3>
                  <p className="text-sm text-green-600 dark:text-green-300">
                    Redirecting to your new learning tasks...
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
        >
          <motion.div variants={cardVariants} className="bg-card border border-border rounded-xl p-4 sm:p-6">
            <div className="flex items-center gap-2 sm:gap-3 mb-3">
              <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Headphones className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-medium text-sm sm:text-base text-card-foreground">Audio Learning</h3>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Speak naturally and get instant feedback on pronunciation and comprehension.
            </p>
          </motion.div>

          <motion.div variants={cardVariants} className="bg-card border border-border rounded-xl p-4 sm:p-6">
            <div className="flex items-center gap-2 sm:gap-3 mb-3">
              <div className="p-1.5 sm:p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <Zap className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-sm sm:text-base text-card-foreground">Real-time Processing</h3>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Advanced AI processes your speech in real-time to create personalized tasks.
            </p>
          </motion.div>

          <motion.div variants={cardVariants} className="bg-card border border-border rounded-xl p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
            <div className="flex items-center gap-2 sm:gap-3 mb-3">
              <div className="p-1.5 sm:p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <Target className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-sm sm:text-base text-card-foreground">Adaptive Difficulty</h3>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Tasks automatically adjust to your skill level for optimal learning progress.
            </p>
          </motion.div>
        </motion.div>
      </motion.div>
    </MainLayout>
  )
}

export default BeginLearningComponent
